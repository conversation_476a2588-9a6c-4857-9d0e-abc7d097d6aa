class ApplicationController < ActionController::Base
  layout :layout_by_devise_scope # Renamed for clarity, will add Devise logic later

  private

  def layout_by_devise_scope
    # For now, all pages will use the public layout.
    # We will change this logic once <PERSON><PERSON> (or your auth system) is in place.
    "public_base"

    # Example of future logic (you'll uncomment and adapt this later):
    # if devise_controller? && resource_name == :user && !user_signed_in?
    #   # This targets <PERSON><PERSON>'s own pages like sign_in, sign_up, password_reset
    #   # when the user is not yet signed in.
    #   "public_base"
    # elsif user_signed_in?
    #   "private_base"
    # else
    #   "public_base"
    # end
  end
end