<%# app/views/layouts/public_base.html.erb %>
<!DOCTYPE html>
<html>
  <head>
    <title>VisualReadingOnline</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%# Link to your compiled Tailwind CSS. The path might be slightly different
        depending on your Rails version and esbuild/propshaft setup.
        Rails 7 default with propshaft usually uses "application.tailwind.css"
        and then "application.css" if you have other custom css.
        Check your app/assets/config/manifest.js if unsure.
    %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%# If your tailwind output is directly 'application.css' in app/assets/builds: %>
    <%# If you only have tailwind css and it's compiled to application.css in builds then this is fine: %>
    <%# <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %> %>

    <%# Ensure your esbuild output (application.js) is included for JS %>
    <%= javascript_include_tag "application", "data-turbo-track": "reload", defer: true %>
  </head>

  <body class="flex flex-col min-h-screen bg-slate-50 text-slate-800 antialiased">
    <%= render 'layouts/public_header' %>

    <main class="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <%# Flash messages - good to have these in your base layout %>
      <% flash.each do |type, msg| %>
        <% type_class = case type.to_s
                        when 'notice' then 'bg-blue-100 border-blue-500 text-blue-700'
                        when 'alert'  then 'bg-red-100 border-red-500 text-red-700'
                        else 'bg-gray-100 border-gray-500 text-gray-700'
                        end %>
        <div class="border-l-4 p-4 <%= type_class %>" role="alert">
          <p><%= msg %></p>
        </div>
      <% end %>

      <%= yield %>
    </main>

    <%= render 'layouts/public_footer' %>
  </body>
</html>