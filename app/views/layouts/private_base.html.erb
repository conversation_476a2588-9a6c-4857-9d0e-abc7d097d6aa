<%# app/views/layouts/private_base.html.erb %>
<!DOCTYPE html>
<html>
  <head>
    <title>VRO Dashboard - VisualReadingOnline</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= javascript_include_tag "application", "data-turbo-track": "reload", defer: true %>
  </head>

  <body class="flex flex-col min-h-screen bg-slate-100 text-slate-900 antialiased">
    <%= render 'layouts/private_header' %>

    <main class="flex-grow container mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <%# Flash messages %>
      <% flash.each do |type, msg| %>
        <% type_class = case type.to_s
                        when 'notice' then 'bg-green-100 border-green-500 text-green-700'
                        when 'alert'  then 'bg-yellow-100 border-yellow-500 text-yellow-700'
                        else 'bg-gray-100 border-gray-500 text-gray-700'
                        end %>
        <div class="border-l-4 p-4 <%= type_class %>" role="alert">
          <p><%= msg %></p>
        </div>
      <% end %>

      <%= yield %>
    </main>

    <%= render 'layouts/private_footer' %>
  </body>
</html>